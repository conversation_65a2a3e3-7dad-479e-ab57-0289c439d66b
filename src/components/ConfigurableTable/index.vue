<template>
  <div class="configurable-table">
    <div class="table-toolbar-wrapper">
      <div class="table-toolbar">
        <div class="table-toolbar-solt">
          <slot name="toolbar-actions"></slot>
        </div>
        <div class="table-toolbar-default">
          <!-- 列配置组件 -->
          <ColumnConfig
            v-if="showColumnConfig"
            :columns="props.columns"
            @changeVisibleColumns="handleColumnsUpdate"
          />
          <!-- 行高配置组件 -->
          <LineHeight v-if="showLineHeight" @change="handleLineHeightChange" />
          <slot name="toolbar-customs"></slot>
        </div>
      </div>
      <div class="table-toolbar-right">
        <slot name="toolbar-right"></slot>
      </div>
    </div>
    <!-- 表格主体 -->
    <a-table
      :row-selection="tableRowSelection"
      :scroll="{ x: 3200, y: 600 }"
      :columns="tableColumns"
      :data-source="dataSource"
      :loading="loading"
      :customRow="setRowStyle"
      :pagination="tablePagination"
      v-bind="$attrs"
    >
      <!-- 传递所有插槽 -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>

      <!-- 默认操作列 -->
      <template #bodyCell="{ column, record, index }">
        <slot name="bodyCell" :column="column" :record="record" :index="index">
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a v-for="action in getRowActions(record)" :key="action.key" @click="action.onClick">
                {{ action.label }}
              </a>
            </a-space>
          </template>
        </slot>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { TableColumn } from '@/common/types'
import { useTableConfig } from './useTableConfig'
import ColumnConfig from '../ColumnConfig/index.vue'
import LineHeight from '../LineHeight/index.vue'

/**
 * 表格操作按钮接口
 */
interface ActionButton {
  label: string
  key: string
  type?: 'primary' | 'default' | 'link'
  icon?: any
  onClick: (record?: any) => void
}

interface Props {
  columns: TableColumn[] // 表格列配置
  dataSource: any[] // 数据源
  showColumnConfig?: boolean // 是否显示列配置
  showLineHeight?: boolean // 是否显示行高配置
  rowSelection?: boolean // 是否支持行选择
  loading?: boolean // 加载状态
  pagination?: any // 分页配置
  rowActions?: ActionButton[] // 行操作按钮
}

const props = withDefaults(defineProps<Props>(), {
  showColumnConfig: true,
  showLineHeight: true,
  rowSelection: false,
  rowActions: () => [],
})

const {
  columns: tableColumns,
  updateColumns,
  setRowStyle,
  updateLineHeight,
} = useTableConfig(props.columns)

// 计算属性：分页配置
const tablePagination = computed(() => {
  return props.pagination
})
// 计算属性：行选择配置
const tableRowSelection = computed(() => {
  return undefined
})

/**
 * 获取行操作按钮
 */
const getRowActions = (record: any) => {
  return props.rowActions.map((action) => ({
    ...action,
    onClick: () => action.onClick(record),
  }))
}

/**
 * 处理列配置更新
 */
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  updateColumns(newColumns)
}

/**
 * 处理行高变化
 */
const handleLineHeightChange = (height: string) => {
  updateLineHeight(height as any)
}
</script>

<style lang="scss" scoped></style>
