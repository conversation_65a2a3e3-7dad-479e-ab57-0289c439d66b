import { ref, computed } from 'vue'
import type { TableColumn } from '@/common/types'
import type { TableConfig } from '../types'
import { createRowStyle, filterVisibleColumns } from '../utils/tableUtils'

/**
 * 表格配置管理 Composable
 * 职责：管理表格列配置、行高设置、行选择等功能
 */
export function useTableConfig(initialColumns: TableColumn[]) {
  // 表格列配置
  const columns = ref<TableColumn[]>([...initialColumns])

  // 行高设置
  const lineHeight = ref<'low' | 'medium' | 'high'>('medium')

  // 选中的行keys
  const selectedRowKeys = ref<(string | number)[]>([])

  // 加载状态
  const loading = ref(false)

  // 计算属性：可见的列
  const visibleColumns = computed(() => filterVisibleColumns(columns.value))

  // 计算属性：行样式
  const rowStyle = computed(() => createRowStyle(lineHeight.value))

  // 计算属性：是否有选中行
  const hasSelectedRows = computed(() => selectedRowKeys.value.length > 0)

  // 计算属性：选中行数量
  const selectedCount = computed(() => selectedRowKeys.value.length)

  /**
   * 更新列配置
   * @param newColumns 新的列配置
   */
  const updateColumns = (newColumns: TableColumn[]) => {
    columns.value = [...newColumns]
  }

  /**
   * 更新列可见性
   * @param visibleKeys 可见列的key数组
   */
  const updateColumnsVisibility = (visibleKeys: string[]) => {
    columns.value = columns.value.map((column) => ({
      ...column,
      visible: visibleKeys.includes(column.key as string),
    }))
  }

  /**
   * 更新行高
   * @param height 行高设置
   */
  const updateLineHeight = (height: 'low' | 'medium' | 'high') => {
    lineHeight.value = height
  }

  /**
   * 设置行样式（用于表格的customRow属性）
   */
  const setRowStyle = () => {
    return rowStyle.value
  }

  /**
   * 处理行选择变化
   * @param keys 选中的行keys
   */
  const handleSelectionChange = (keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedRowKeys.value = []
  }

  /**
   * 全选/取消全选
   * @param dataSource 数据源
   * @param keyField 主键字段名
   */
  const toggleSelectAll = (dataSource: any[], keyField = 'id') => {
    if (selectedRowKeys.value.length === dataSource.length) {
      clearSelection()
    } else {
      selectedRowKeys.value = dataSource.map((item) => item[keyField])
    }
  }

  /**
   * 获取选中的数据
   * @param dataSource 数据源
   * @param keyField 主键字段名
   */
  const getSelectedData = (dataSource: any[], keyField = 'id') => {
    return dataSource.filter((item) => selectedRowKeys.value.includes(item[keyField]))
  }

  /**
   * 重置表格配置
   */
  const resetConfig = () => {
    columns.value = [...initialColumns]
    lineHeight.value = 'medium'
    selectedRowKeys.value = []
    loading.value = false
  }

  /**
   * 获取表格配置对象
   */
  const getTableConfig = (): TableConfig => {
    return {
      columns: columns.value,
      lineHeight: lineHeight.value,
      selectedRowKeys: selectedRowKeys.value,
    }
  }

  /**
   * 设置表格配置
   * @param config 表格配置对象
   */
  const setTableConfig = (config: Partial<TableConfig>) => {
    if (config.columns) {
      columns.value = [...config.columns]
    }
    if (config.lineHeight) {
      lineHeight.value = config.lineHeight
    }
    if (config.selectedRowKeys) {
      selectedRowKeys.value = [...config.selectedRowKeys]
    }
  }

  return {
    // 响应式数据
    columns,
    lineHeight,
    selectedRowKeys,
    loading,

    // 计算属性
    visibleColumns,
    rowStyle,
    hasSelectedRows,
    selectedCount,

    // 方法
    updateColumns,
    updateColumnsVisibility,
    updateLineHeight,
    setRowStyle,
    handleSelectionChange,
    clearSelection,
    toggleSelectAll,
    getSelectedData,
    resetConfig,
    getTableConfig,
    setTableConfig,
  }
}
