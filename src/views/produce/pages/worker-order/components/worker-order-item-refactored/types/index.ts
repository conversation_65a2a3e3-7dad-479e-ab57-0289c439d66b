import type { Dayjs } from 'dayjs'
import type { TableColumn } from '@/common/types'

/**
 * 工单状态枚举
 */
export enum OrderStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PAUSED = 'paused',
}

/**
 * 产品信息接口
 */
export interface ProductInfo {
  id: string
  productNo: string
  productName: string
  productSpec: string
  stockCount: number
  unit: string
}

/**
 * 工单基本信息接口
 */
export interface WorkerOrder {
  id?: string
  orderNo: string
  planTime: [Dayjs, Dayjs]
  status: OrderStatus
  productId: string
  productInfo?: ProductInfo
  planCount: number
  remark?: string
  relatedOrder?: string
  // 统计信息
  goodCount?: number
  badCount?: number
  defectRate?: number
}

/**
 * 生产任务接口
 */
export interface ProductionTask {
  id: string
  taskNo: string
  processName: string
  processNo: string
  reportPermission: string
  assignmentList: string
  workCountRatio: string
  planCount: number
  goodCount: number
  badCount: number
  badList: string
  planStartTime: string
  planEndTime: string
  actualStartTime?: string
  actualEndTime?: string
  status: TaskStatus
}

/**
 * 物料信息接口
 */
export interface MaterialItem {
  id: string
  productNo: string
  productName: string
  productSpec: string
  productAttr: string
  stockCount: number
  unit: string
  unitUsage: number
  demandCount: number
  unclaimedCount: number
  actualUsageCount: number
  claimedCount: number
  returnedCount: number
}

/**
 * 表格配置接口
 */
export interface TableConfig {
  columns: TableColumn[]
  lineHeight: 'low' | 'medium' | 'high'
  selectedRowKeys: (string | number)[]
}

/**
 * 抽屉配置接口
 */
export interface DrawerConfig {
  open: boolean
  width: string
  isMaximize: boolean
}

/**
 * 标签页类型
 */
export type TabKey = 'base' | 'custom' | 'task' | 'bom'

/**
 * 组件操作类型
 */
export type ComponentType = 'create' | 'edit' | 'view'

/**
 * 表格操作按钮接口
 */
export interface ActionButton {
  label: string
  key: string
  type?: 'primary' | 'default' | 'link'
  icon?: any
  onClick: (record?: any) => void
}

/**
 * 表单验证规则接口
 */
export interface FormRules {
  [key: string]: any[]
}

/**
 * 统计数据接口
 */
export interface StatisticsData {
  goodCount: number
  badCount: number
  defectRate: number
  label?: string
}

/**
 * 锚点映射接口
 */
export interface AnchorMap {
  [key: string]: string
}

/**
 * 滚动配置接口
 */
export interface ScrollConfig {
  activeTab: TabKey
  isScrollingToAnchor: boolean
  anchorMap: AnchorMap
}
