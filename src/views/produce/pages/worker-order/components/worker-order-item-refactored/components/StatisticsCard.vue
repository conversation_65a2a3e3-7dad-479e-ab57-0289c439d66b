<template>
  <div class="detail-box plan-count-extra">
    <div class="info-block plan-count__item">
      <div class="amount good">{{ formatNumber(data.goodCount) }}</div>
      <div class="label">良品数</div>
    </div>
    <div class="info-block plan-count__item">
      <div class="amount bad">{{ formatNumber(data.badCount) }}</div>
      <div class="label">不良品数</div>
    </div>
    <div class="info-block plan-count__item">
      <div class="amount rate">{{ data.defectRate }}%</div>
      <div class="label">不良品率</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StatisticsData } from '../types'
import { formatNumber } from '@/utils'

/**
 * 统计信息卡片组件
 * 职责：展示生产统计数据（良品数、不良品数、不良品率）
 */

interface Props {
  data: StatisticsData
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.detail-box {
  display: flex;
  margin-top: 4px;
  gap: 8px;

  .info-block {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
  }
}

.plan-count-extra {
  .plan-count__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;

    .amount {
      font-size: 16px;
      font-weight: 600;
      line-height: 1;
      margin-bottom: 4px;

      &.good {
        color: #52c41a;
      }

      &.bad {
        color: #ff4d4f;
      }

      &.rate {
        color: #fa8c16;
      }
    }

    .label {
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
