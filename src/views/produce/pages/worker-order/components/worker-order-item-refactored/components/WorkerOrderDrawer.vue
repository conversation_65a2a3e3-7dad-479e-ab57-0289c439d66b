<template>
  <a-drawer
    :open="open"
    :closable="false"
    :headerStyle="{ padding: '0 24px' }"
    :body-style="{ padding: 0 }"
    :width="drawerWidth"
    @close="handleClose"
  >
    <!-- 抽屉标题栏 -->
    <template #title>
      <div class="drawer-header">
        <div class="drawer-header-title">{{ drawerTitle }}</div>

        <!-- 标签页导航 -->
        <div class="drawer-header-tabs">
          <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
            <a-tab-pane key="base" tab="基本信息" />
            <a-tab-pane key="custom" tab="自定义信息" />
            <a-tab-pane key="task" tab="生产任务" />
            <a-tab-pane key="bom" tab="用料清单" />
          </a-tabs>
        </div>

        <!-- 操作按钮 -->
        <div class="drawer-header-btn">
          <a-tooltip>
            <template #title>{{ isMaximize ? '收起' : '全屏' }}</template>
            <a-button
              class="drawer-header-btn-item"
              type="text"
              :icon="h(isMaximize ? ShrinkOutlined : ArrowsAltOutlined)"
              @click="handleMaximize"
            />
          </a-tooltip>
          <a-divider type="vertical" />
          <a-button
            class="drawer-header-btn-item"
            type="text"
            :icon="h(CloseOutlined)"
            @click="handleClose"
          />
        </div>
      </div>
    </template>

    <!-- 抽屉内容区域 -->
    <div class="drawer-content" ref="scrollContainer">
      <!-- 基本信息区域 -->
      <div class="drawer-content-item" id="base-section">
        <div class="drawer-content-item__title">基本信息</div>
        <div class="drawer-content-item__content">
          <slot name="base-content">
            <!-- 默认插槽内容或者可以直接使用 WorkerOrderForm 组件 -->
          </slot>
        </div>
      </div>

      <!-- 自定义信息区域 -->
      <div class="drawer-content-item" id="custom-section">
        <div class="drawer-content-item__title">自定义信息</div>
        <div class="drawer-content-item__content">
          <slot name="custom-content">
            <!-- 自定义信息内容 -->
          </slot>
        </div>
      </div>

      <!-- 生产任务区域 -->
      <div class="drawer-content-item" id="task-section">
        <div class="drawer-content-item__title">生产任务</div>
        <div class="drawer-content-item__content">
          <slot name="task-content">
            <!-- 生产任务表格内容 -->
          </slot>
        </div>
      </div>

      <!-- 用料清单区域 -->
      <div class="drawer-content-item" id="bom-section">
        <div class="drawer-content-item__title">用料清单</div>
        <div class="drawer-content-item__content">
          <slot name="bom-content">
            <!-- 用料清单表格内容 -->
          </slot>
        </div>
      </div>
    </div>

    <!-- 抽屉底部操作栏 -->
    <template #footer>
      <div class="drawer-footer">
        <slot name="footer-actions">
          <!-- 默认操作按钮 -->
          <a-button class="drawer-footer__btn" type="default" @click="handleClose"> 取消 </a-button>
          <a-button
            class="drawer-footer__btn"
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            {{ submitButtonText }}
          </a-button>
        </slot>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { CloseOutlined, ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons-vue'
import type { ComponentType, TabKey } from '../types'
import { useDrawerState } from '../composables/useDrawerState'
import { useScrollAnchor } from '../composables/useScrollAnchor'

/**
 * 工单抽屉主容器组件
 * 职责：
 * 1. 管理抽屉的开关、最大化状态
 * 2. 处理标签页导航和滚动锚点
 * 3. 提供内容区域的插槽
 * 4. 统一的操作按钮管理
 */

interface Props {
  open: boolean
  orderId?: string | number
  type: ComponentType
  submitLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'create',
  submitLoading: false,
})

const emit = defineEmits<{
  'update:open': [open: boolean]
  close: []
  submit: []
  'tab-change': [key: TabKey]
}>()

// 滚动容器引用
const scrollContainer = ref<HTMLElement>()

// 使用抽屉状态管理
const { isMaximize, drawerWidth, handleMaximize } = useDrawerState()

// 使用滚动锚点管理
const { activeTab, handleTabChange: onTabChange } = useScrollAnchor(scrollContainer)

// 计算属性：抽屉标题
const drawerTitle = computed(() => {
  const titleMap = {
    create: '创建工单',
    edit: '编辑工单',
    view: '工单详情',
  }
  return titleMap[props.type]
})

// 计算属性：提交按钮文本
const submitButtonText = computed(() => {
  const textMap = {
    create: '创建',
    edit: '保存',
    view: '确定',
  }
  return textMap[props.type]
})

/**
 * 处理标签页切换
 */
const handleTabChange = (key: string) => {
  const tabKey = key as TabKey
  onTabChange(tabKey)
  emit('tab-change', tabKey)
}

/**
 * 处理抽屉关闭
 */
const handleClose = () => {
  emit('close')
  emit('update:open', false)
}

/**
 * 处理提交操作
 */
const handleSubmit = () => {
  emit('submit')
}

// 暴露方法给父组件
defineExpose({
  scrollToTab: onTabChange,
  maximize: handleMaximize,
  close: handleClose,
})
</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .drawer-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #111;
  }

  .drawer-header-btn {
    display: flex;
    align-items: center;
  }
}

.drawer-content {
  padding: 24px;
  height: calc(100vh - 100px); /* 减去header和footer的高度 */
  overflow-y: auto;
  scroll-behavior: smooth;

  .drawer-content-item {
    margin-bottom: 16px;

    &__title {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.drawer-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .drawer-footer__btn {
    min-width: 80px;
  }
}
</style>
