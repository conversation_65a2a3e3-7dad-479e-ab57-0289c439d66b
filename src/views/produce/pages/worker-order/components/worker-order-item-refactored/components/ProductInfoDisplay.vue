<template>
  <div class="product-detail detail-box">
    <div class="info-block product-detail__left">
      <div class="product-row">
        <span class="product-row__label">产品编号</span>
        <span class="product-row__value">{{ product.productNo }}</span>
      </div>
      <div class="product-row">
        <span class="product-row__label">产品名称</span>
        <span class="product-row__value">{{ product.productName }}</span>
      </div>
      <div class="product-row">
        <span class="product-row__label">产品规格</span>
        <span class="product-row__value">{{ product.productSpec }}</span>
      </div>
    </div>
    <div class="info-block product-detail__right">
      <div class="stock-amount">{{ formatNumber(product.stockCount) }}</div>
      <div class="stock-unit-text">
        库存数量(<span>{{ product.unit }}</span
        >)
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProductInfo } from '../types'
import { formatNumber } from '@/utils'

/**
 * 产品信息展示组件
 * 职责：展示选中产品的详细信息和库存数量
 */

interface Props {
  product: ProductInfo
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.detail-box {
  display: flex;
  margin-top: 4px;

  .info-block {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
  }
}

.product-detail {
  gap: 12px;

  &__left {
    flex: 1;
  }

  &__right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 120px;
  }
}

.product-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    color: #666;
    font-size: 12px;
  }

  &__value {
    color: #333;
    font-size: 12px;
    font-weight: 500;
  }
}

.stock-amount {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  line-height: 1;
}

.stock-unit-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-align: center;

  span {
    color: #333;
  }
}
</style>
